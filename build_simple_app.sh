#!/bin/bash

# 全新的简单应用打包脚本
# 避免代码签名问题，使用更简单的方案

set -e

APP_NAME="Dou-flow"
APP_BUNDLE="${APP_NAME}.app"
APP_CONTENTS="${APP_BUNDLE}/Contents"
APP_MACOS="${APP_CONTENTS}/MacOS"
APP_RESOURCES="${APP_CONTENTS}/Resources"

echo "🎯 开始构建简单版 ${APP_NAME}.app..."

# 清理旧的应用包
if [ -d "$APP_BUNDLE" ]; then
    echo "🧹 清理旧应用包..."
    rm -rf "$APP_BUNDLE"
fi

# 创建应用包结构
echo "📁 创建应用包结构..."
mkdir -p "$APP_MACOS"
mkdir -p "$APP_RESOURCES"

# 创建Info.plist
echo "📄 创建 Info.plist..."
cat > "${APP_CONTENTS}/Info.plist" << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>launcher</string>
    <key>CFBundleIconFile</key>
    <string>app_icon</string>
    <key>CFBundleIdentifier</key>
    <string>com.douba.douflow</string>
    <key>CFBundleName</key>
    <string>Dou-flow</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
    <key>CFBundleVersion</key>
    <string>1.0.0</string>
    <key>LSMinimumSystemVersion</key>
    <string>10.15</string>
    <key>LSArchitecturePriority</key>
    <array>
        <string>arm64</string>
        <string>x86_64</string>
    </array>
    <key>NSMicrophoneUsageDescription</key>
    <string>需要使用麦克风来录制音频进行语音识别</string>
    <key>NSAppleEventsUsageDescription</key>
    <string>需要访问系统事件以支持快捷键和自动粘贴功能</string>
    <key>NSAccessibilityUsageDescription</key>
    <string>需要辅助功能权限来支持自动粘贴和系统集成</string>
    <key>NSHighResolutionCapable</key>
    <true/>
</dict>
</plist>
EOF

# 复制源代码
echo "📦 复制源代码..."
cp -r src/ "$APP_RESOURCES/"
cp -r *.txt "$APP_RESOURCES/" 2>/dev/null || true
cp -r *.wav "$APP_RESOURCES/" 2>/dev/null || true
cp -r *.png "$APP_RESOURCES/" 2>/dev/null || true
cp -r *.svg "$APP_RESOURCES/" 2>/dev/null || true

# 复制图标
echo "🎨 复制应用图标..."
if [ -f "iconset.icns" ]; then
    cp "iconset.icns" "${APP_RESOURCES}/app_icon.icns"
fi

# 创建Python启动器
echo "🚀 创建Python启动器..."
cat > "${APP_MACOS}/launcher" << 'EOF'
#!/usr/bin/env python3
"""
Dou-flow 简单启动器
使用系统Python直接启动应用
"""

import sys
import os
import subprocess
from pathlib import Path

def show_error(message):
    """显示错误对话框"""
    script = f'''
    display dialog "{message}" buttons {{"确定"}} default button "确定" with icon stop with title "Dou-flow 启动错误"
    '''
    subprocess.run(["osascript", "-e", script])

def show_info(message):
    """显示信息对话框"""
    script = f'''
    display dialog "{message}" buttons {{"确定"}} default button "确定" with icon note with title "Dou-flow"
    '''
    subprocess.run(["osascript", "-e", script])

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        show_error(f"Python版本过低: {sys.version}\\n\\n需要Python 3.8或更高版本")
        return False
    return True

def install_dependencies():
    """安装依赖"""
    try:
        # 检查关键依赖
        import PyQt6
        import sounddevice
        import torch
        import funasr
        return True
    except ImportError as e:
        missing_module = str(e).split("'")[1] if "'" in str(e) else "unknown"
        
        show_info(f"检测到缺失依赖: {missing_module}\\n\\n正在自动安装，请稍候...")
        
        # 安装依赖
        packages = [
            "PyQt6",
            "sounddevice", 
            "torch",
            "torchaudio",
            "funasr",
            "modelscope",
            "numpy",
            "pynput",
            "pyperclip",
            "requests",
            "tqdm"
        ]
        
        for package in packages:
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", package], 
                             check=True, capture_output=True)
            except subprocess.CalledProcessError:
                show_error(f"安装依赖失败: {package}\\n\\n请手动安装或检查网络连接")
                return False
        
        show_info("依赖安装完成！")
        return True

def main():
    """主函数"""
    # 获取应用包路径
    launcher_path = Path(__file__).resolve()
    app_resources = launcher_path.parent.parent / "Resources"
    
    # 切换到Resources目录
    os.chdir(app_resources)
    
    # 添加Resources目录到Python路径（因为源代码直接在Resources中）
    if str(app_resources) not in sys.path:
        sys.path.insert(0, str(app_resources))
    
    # 设置环境变量
    os.environ['DISABLE_INPUT_SOURCE_CHECK'] = '1'
    os.environ['LAUNCHED_FROM_APP_BUNDLE'] = '1'
    os.environ['PYTHONUNBUFFERED'] = '1'
    os.environ['QT_MAC_DISABLE_FOREGROUND_APPLICATION_TRANSFORM'] = '1'
    
    # 检查Python版本
    if not check_python_version():
        return 1
    
    # 安装依赖
    if not install_dependencies():
        return 1
    
    try:
        # 导入并运行主程序
        import main
        return 0
    except Exception as e:
        show_error(f"应用启动失败:\\n\\n{str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
EOF

# 设置执行权限
echo "🔧 设置执行权限..."
chmod +x "${APP_MACOS}/launcher"

# 清理扩展属性
echo "🧹 清理扩展属性..."
xattr -cr "$APP_BUNDLE"

# 不进行代码签名，避免签名问题

echo ""
echo "✅ 简单版应用构建完成！"
echo "📦 应用位置: $APP_BUNDLE"
echo "📏 应用大小: $(du -sh "$APP_BUNDLE" | cut -f1)"
echo "🎯 特性: 使用系统Python，自动安装依赖"
echo "🚀 现在可以双击 $APP_BUNDLE 启动应用"
echo ""
echo "💡 注意: 首次运行会自动安装依赖包"
