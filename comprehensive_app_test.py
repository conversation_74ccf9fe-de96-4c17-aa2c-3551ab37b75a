#!/usr/bin/env python3
"""
Dou-flow 应用启动全面测试脚本
测试应用是否能真正启动并正常工作
"""

import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path

class AppTester:
    def __init__(self):
        self.app_path = Path("Dou-flow.app")
        self.executable_path = self.app_path / "Contents/MacOS/Dou-flow"
        self.test_results = []
        self.app_process = None
        
    def log_result(self, test_name, success, message=""):
        status = "✅ PASS" if success else "❌ FAIL"
        result = f"{status} {test_name}: {message}"
        print(result)
        self.test_results.append((test_name, success, message))
        
    def test_app_structure(self):
        """测试应用包结构"""
        print("\n=== 测试应用包结构 ===")
        
        # 检查应用包是否存在
        if not self.app_path.exists():
            self.log_result("应用包存在性", False, "Dou-flow.app 不存在")
            return False
        self.log_result("应用包存在性", True, f"找到应用包: {self.app_path}")
        
        # 检查可执行文件
        if not self.executable_path.exists():
            self.log_result("可执行文件存在性", False, f"可执行文件不存在: {self.executable_path}")
            return False
        self.log_result("可执行文件存在性", True, f"找到可执行文件: {self.executable_path}")
        
        # 检查可执行权限
        if not os.access(self.executable_path, os.X_OK):
            self.log_result("可执行权限", False, "可执行文件没有执行权限")
            return False
        self.log_result("可执行权限", True, "可执行文件有执行权限")
        
        # 检查Info.plist
        info_plist = self.app_path / "Contents/Info.plist"
        if not info_plist.exists():
            self.log_result("Info.plist存在性", False, "Info.plist 不存在")
            return False
        self.log_result("Info.plist存在性", True, "Info.plist 存在")
        
        # 检查Resources目录
        resources_dir = self.app_path / "Contents/Resources"
        if not resources_dir.exists():
            self.log_result("Resources目录", False, "Resources 目录不存在")
            return False
        self.log_result("Resources目录", True, "Resources 目录存在")
        
        # 检查main.py
        main_py = resources_dir / "main.py"
        if not main_py.exists():
            self.log_result("main.py存在性", False, "main.py 不存在")
            return False
        self.log_result("main.py存在性", True, "main.py 存在")
        
        return True
    
    def test_executable_syntax(self):
        """测试可执行文件语法"""
        print("\n=== 测试可执行文件语法 ===")
        
        try:
            # 使用bash检查语法
            result = subprocess.run(
                ["bash", "-n", str(self.executable_path)],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                self.log_result("Bash语法检查", True, "语法正确")
                return True
            else:
                self.log_result("Bash语法检查", False, f"语法错误: {result.stderr}")
                return False
                
        except Exception as e:
            self.log_result("Bash语法检查", False, f"检查失败: {e}")
            return False
    
    def test_dependencies(self):
        """测试依赖环境"""
        print("\n=== 测试依赖环境 ===")
        
        # 检查conda
        try:
            result = subprocess.run(["conda", "--version"], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.log_result("Conda可用性", True, result.stdout.strip())
            else:
                self.log_result("Conda可用性", False, "conda命令不可用")
                return False
        except Exception as e:
            self.log_result("Conda可用性", False, f"conda检查失败: {e}")
            return False
        
        # 检查conda环境
        try:
            result = subprocess.run(
                ["conda", "env", "list"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            if "wispr-flow-python311" in result.stdout:
                self.log_result("Conda环境", True, "wispr-flow-python311 环境存在")
            else:
                self.log_result("Conda环境", False, "wispr-flow-python311 环境不存在")
                return False
        except Exception as e:
            self.log_result("Conda环境", False, f"环境检查失败: {e}")
            return False
        
        return True
    
    def test_app_launch_direct(self):
        """测试直接启动可执行文件"""
        print("\n=== 测试直接启动可执行文件 ===")
        
        try:
            # 启动应用并等待一段时间
            process = subprocess.Popen(
                [str(self.executable_path)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=str(self.app_path.parent)
            )
            
            # 等待5秒看应用是否能启动
            try:
                stdout, stderr = process.communicate(timeout=5)
                
                if process.returncode == 0:
                    self.log_result("直接启动", True, "应用正常启动并退出")
                    if stdout:
                        print(f"标准输出: {stdout}")
                    if stderr:
                        print(f"错误输出: {stderr}")
                    return True
                else:
                    self.log_result("直接启动", False, f"应用异常退出，返回码: {process.returncode}")
                    if stderr:
                        print(f"错误输出: {stderr}")
                    return False
                    
            except subprocess.TimeoutExpired:
                # 应用可能在后台运行
                self.log_result("直接启动", True, "应用启动后在后台运行")
                process.terminate()
                try:
                    process.wait(timeout=3)
                except subprocess.TimeoutExpired:
                    process.kill()
                return True
                
        except Exception as e:
            self.log_result("直接启动", False, f"启动失败: {e}")
            return False
    
    def test_app_launch_open(self):
        """测试使用open命令启动应用"""
        print("\n=== 测试使用open命令启动应用 ===")
        
        try:
            # 使用open命令启动应用
            process = subprocess.Popen(
                ["open", str(self.app_path)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待open命令完成
            stdout, stderr = process.communicate(timeout=10)
            
            if process.returncode == 0:
                self.log_result("Open命令启动", True, "open命令执行成功")
                
                # 等待一段时间，然后检查应用是否在运行
                time.sleep(3)
                
                # 检查是否有相关进程
                try:
                    ps_result = subprocess.run(
                        ["ps", "aux"],
                        capture_output=True,
                        text=True,
                        timeout=5
                    )

                    app_running = False
                    if ps_result.returncode == 0:
                        for line in ps_result.stdout.split('\n'):
                            if 'Dou-flow' in line or 'main.py' in line:
                                app_running = True
                                self.log_result("应用进程检测", True, f"发现应用进程: {line.strip()}")
                                break

                    if not app_running:
                        self.log_result("应用进程检测", False, "未发现应用进程")

                except Exception as e:
                    self.log_result("应用进程检测", False, f"进程检测失败: {e}")
                
                return True
            else:
                self.log_result("Open命令启动", False, f"open命令失败: {stderr}")
                return False
                
        except Exception as e:
            self.log_result("Open命令启动", False, f"启动失败: {e}")
            return False
    
    def test_log_files(self):
        """检查日志文件"""
        print("\n=== 检查日志文件 ===")
        
        log_files = [
            "~/dou-flow-startup.log",
            "~/dou-flow-error.log", 
            "~/dou-flow-debug.log"
        ]
        
        for log_file in log_files:
            log_path = Path(log_file).expanduser()
            if log_path.exists():
                self.log_result(f"日志文件 {log_file}", True, f"文件存在，大小: {log_path.stat().st_size} bytes")
                
                # 显示最后几行
                try:
                    with open(log_path, 'r') as f:
                        lines = f.readlines()
                        if lines:
                            print(f"  最后几行内容:")
                            for line in lines[-3:]:
                                print(f"    {line.strip()}")
                except Exception as e:
                    print(f"  读取日志失败: {e}")
            else:
                self.log_result(f"日志文件 {log_file}", False, "文件不存在")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始 Dou-flow 应用全面测试")
        print("=" * 50)
        
        tests = [
            ("应用包结构", self.test_app_structure),
            ("可执行文件语法", self.test_executable_syntax),
            ("依赖环境", self.test_dependencies),
            ("直接启动测试", self.test_app_launch_direct),
            ("Open命令启动测试", self.test_app_launch_open),
        ]
        
        all_passed = True
        for test_name, test_func in tests:
            try:
                result = test_func()
                if not result:
                    all_passed = False
            except Exception as e:
                self.log_result(test_name, False, f"测试异常: {e}")
                all_passed = False
        
        # 检查日志文件
        self.test_log_files()
        
        # 输出总结
        print("\n" + "=" * 50)
        print("📊 测试结果总结")
        print("=" * 50)
        
        passed = sum(1 for _, success, _ in self.test_results if success)
        total = len(self.test_results)
        
        for test_name, success, message in self.test_results:
            status = "✅" if success else "❌"
            print(f"{status} {test_name}: {message}")
        
        print(f"\n总计: {passed}/{total} 测试通过")
        
        if all_passed:
            print("🎉 所有测试通过！应用应该能正常启动。")
        else:
            print("⚠️ 部分测试失败！应用可能无法正常启动。")
            print("\n🔧 建议检查失败的测试项并修复相关问题。")
        
        return all_passed

def main():
    """主函数"""
    # 切换到正确的目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    tester = AppTester()
    success = tester.run_all_tests()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
