#!/usr/bin/env python3
"""
测试简单版应用的脚本
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def test_app_structure():
    """测试应用结构"""
    print("🔍 测试应用结构...")
    
    app_path = Path("Dou-flow.app")
    if not app_path.exists():
        print("❌ 应用包不存在")
        return False
    
    launcher = app_path / "Contents/MacOS/launcher"
    if not launcher.exists():
        print("❌ 启动器不存在")
        return False
    
    if not os.access(launcher, os.X_OK):
        print("❌ 启动器没有执行权限")
        return False
    
    print("✅ 应用结构正确")
    return True

def test_launcher_directly():
    """直接测试启动器"""
    print("🔍 直接测试启动器...")
    
    launcher = Path("Dou-flow.app/Contents/MacOS/launcher")
    
    try:
        # 运行启动器，设置超时
        result = subprocess.run(
            [sys.executable, str(launcher)],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        print(f"返回码: {result.returncode}")
        if result.stdout:
            print(f"标准输出: {result.stdout}")
        if result.stderr:
            print(f"错误输出: {result.stderr}")
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("✅ 启动器运行超时（可能在后台运行GUI）")
        return True
    except Exception as e:
        print(f"❌ 启动器测试失败: {e}")
        return False

def test_open_command():
    """测试open命令"""
    print("🔍 测试open命令...")
    
    try:
        result = subprocess.run(
            ["open", "Dou-flow.app"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            print("✅ open命令执行成功")
            return True
        else:
            print(f"❌ open命令失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ open命令异常: {e}")
        return False

def check_running_processes():
    """检查运行中的进程"""
    print("🔍 检查运行中的进程...")
    
    try:
        result = subprocess.run(
            ["ps", "aux"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        dou_flow_processes = []
        for line in result.stdout.split('\n'):
            if 'launcher' in line or 'Dou-flow' in line or 'main.py' in line:
                if 'grep' not in line:
                    dou_flow_processes.append(line.strip())
        
        if dou_flow_processes:
            print("✅ 发现Dou-flow相关进程:")
            for proc in dou_flow_processes:
                print(f"   {proc}")
            return True
        else:
            print("❌ 未发现Dou-flow相关进程")
            return False
            
    except Exception as e:
        print(f"❌ 进程检查失败: {e}")
        return False

def test_python_import():
    """测试Python模块导入"""
    print("🔍 测试Python模块导入...")
    
    app_resources = Path("Dou-flow.app/Contents/Resources")
    src_dir = app_resources / "src"
    
    if not src_dir.exists():
        print("❌ src目录不存在")
        return False
    
    # 切换到正确的目录
    original_cwd = os.getcwd()
    os.chdir(app_resources)
    
    if str(src_dir) not in sys.path:
        sys.path.insert(0, str(src_dir))
    
    try:
        # 测试关键模块导入
        import main
        print("✅ main模块导入成功")
        
        from app_loader import AppLoader
        print("✅ AppLoader导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    finally:
        os.chdir(original_cwd)

def main():
    """主函数"""
    print("🧪 简单版Dou-flow应用测试")
    print("=" * 50)
    
    tests = [
        ("应用结构", test_app_structure),
        ("Python模块导入", test_python_import),
        ("启动器直接测试", test_launcher_directly),
        ("Open命令测试", test_open_command),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 检查进程
    print(f"\n📋 进程检查:")
    check_running_processes()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed >= total - 1:  # 允许一个测试失败
        print("\n🎉 应用基本可用！")
        print("📱 简单版打包方案成功")
        return 0
    else:
        print(f"\n⚠️ 多个测试失败，需要进一步调试")
        return 1

if __name__ == "__main__":
    sys.exit(main())
