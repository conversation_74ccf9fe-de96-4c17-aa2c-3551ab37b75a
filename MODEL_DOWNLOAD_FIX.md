# 模型下载问题修复

## 问题描述

用户在打包应用中遇到"您选择了取消下载"的错误信息，即使用户没有主动取消下载。应用显示错误对话框并提示需要重新启动应用。

## 问题根因分析

1. **Shell脚本与Python应用缺乏协调**：
   - 打包应用通过shell脚本启动，脚本会检查模型是否存在
   - 如果用户在shell脚本中取消下载，Python应用仍然会启动
   - Python应用在FunASR引擎初始化时发现模型缺失，直接抛出异常

2. **FunASR引擎初始化逻辑问题**：
   - `_ensure_models_available()`方法会自动下载模型，但这是同步的
   - 如果模型不存在，引擎初始化会失败，但没有用户友好的提示

3. **错误处理不够友好**：
   - 应用显示技术性错误信息而不是用户友好的下载对话框
   - 用户无法在应用内重新尝试下载模型

## 解决方案

### 1. 修改应用加载器 (`src/app_loader.py`)

**关键修改**：
- 在加载FunASR引擎前先检查模型是否存在
- 如果模型缺失，发出特殊信号通知主线程显示下载对话框
- 移除FunASR引擎的自动下载逻辑

```python
def _load_funasr_engine(self):
    # 首先检查模型是否存在
    from src.model_downloader import ModelDownloader
    model_downloader = ModelDownloader()
    missing_models = model_downloader.get_missing_models()
    
    if missing_models:
        # 发出信号，让主线程处理模型下载对话框
        self.loading_failed.emit("MODELS_MISSING:" + ",".join(missing_models.keys()))
        return
    
    # 模型存在，正常加载引擎
    # ...
```

### 2. 修改FunASR引擎 (`src/funasr_engine.py`)

**关键修改**：
- 移除初始化时的自动模型下载逻辑
- 只检查模型是否存在，不存在则抛出异常

```python
def __init__(self, settings_manager=None, progress_callback=None):
    # 检查模型文件是否存在，但不自动下载
    asr_model_dir = os.path.join(cache_dir, 'damo', 'speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch')
    
    # ASR模型是必需的
    if not os.path.exists(asr_model_dir):
        raise Exception(f"ASR模型文件不存在: {asr_model_dir}")
```

### 3. 修改主应用 (`src/main.py`)

**关键修改**：
- 在`on_loading_failed`方法中检测模型缺失信号
- 显示用户友好的模型下载对话框
- 提供重新启动应用的选项

```python
def on_loading_failed(self, error_message):
    # 检查是否是模型缺失的情况
    if error_message.startswith("MODELS_MISSING:"):
        missing_models_str = error_message.replace("MODELS_MISSING:", "")
        missing_models = missing_models_str.split(",")
        
        # 显示模型下载对话框
        self._show_model_download_dialog(missing_models)
        return
    # ...
```

### 4. 修改主窗口 (`src/ui/main_window.py`)

**关键修改**：
- 添加显示模型下载对话框的方法
- 确保对话框在主线程中正确显示

```python
def show_model_download_dialog(self, missing_models):
    """显示模型下载对话框"""
    from .model_download_dialog import ModelDownloadDialog
    
    dialog = ModelDownloadDialog(
        parent=self,
        models_to_download=missing_models
    )
    
    dialog.setModal(True)
    dialog.setWindowFlags(dialog.windowFlags() | Qt.WindowType.WindowStaysOnTopHint)
    
    return dialog.exec()
```

## 测试验证

1. **模拟模型缺失**：
   ```bash
   mv src/modelscope/hub/damo src/modelscope/hub/damo_backup
   ```

2. **启动应用**：
   ```bash
   python src/main.py
   ```

3. **验证结果**：
   - 应用检测到模型缺失
   - 显示友好的模型下载对话框
   - 用户可以选择下载或取消
   - 如果取消，显示清晰的提示信息

## 日志输出示例

```
2025-08-05 12:26:57,649 - root - INFO - 检测到缺失的模型: ['asr', 'punc']
2025-08-05 12:26:57,650 - root - ERROR - 组件加载失败: MODELS_MISSING:asr,punc
2025-08-05 12:26:57,666 - root - INFO - 显示模型下载对话框，缺失模型: ['asr', 'punc']
2025-08-05 12:26:57,668 - root - INFO - 主窗口显示模型下载对话框，缺失模型: ['asr', 'punc']
```

## 用户体验改进

1. **友好的错误提示**：用户看到的是模型下载对话框而不是技术错误信息
2. **应用内重试**：用户可以在应用内重新尝试下载模型
3. **清晰的状态反馈**：明确告知用户当前状态和下一步操作
4. **优雅的降级**：如果用户取消下载，应用会显示清晰的说明而不是崩溃

## 总结

这个修复解决了用户在打包应用中遇到的模型下载问题，提供了更好的用户体验：

- ✅ 检测模型缺失并显示友好的下载对话框
- ✅ 用户可以在应用内重新尝试下载
- ✅ 清晰的错误提示和状态反馈
- ✅ 优雅的错误处理，避免应用崩溃
- ✅ 保持与现有模型下载对话框的一致性
