#!/usr/bin/env python3
"""
测试GUI启动的脚本
模拟应用启动过程，检查是否会显示模型下载对话框
"""

import sys
import os
import subprocess
import time
from pathlib import Path

def test_gui_startup():
    """测试GUI启动"""
    print("🧪 测试GUI启动过程")
    
    # 设置环境
    app_resources = Path("Dou-flow.app/Contents/Resources")
    if not app_resources.exists():
        print("❌ 应用包不存在")
        return False
    
    # 切换到Resources目录
    original_cwd = os.getcwd()
    os.chdir(app_resources)
    
    # 添加src到Python路径
    src_dir = Path("src")
    if src_dir not in sys.path:
        sys.path.insert(0, str(src_dir))
    
    # 设置环境变量
    os.environ['DISABLE_INPUT_SOURCE_CHECK'] = '1'
    os.environ['LAUNCHED_FROM_APP_BUNDLE'] = '1'
    os.environ['PYTHONUNBUFFERED'] = '1'
    os.environ['QT_MAC_DISABLE_FOREGROUND_APPLICATION_TRANSFORM'] = '1'
    
    try:
        print("📦 导入PyQt6...")
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import QTimer
        
        print("✅ PyQt6导入成功")
        
        # 创建QApplication
        app = QApplication([])
        print("✅ QApplication创建成功")
        
        # 导入主模块
        print("📦 导入主模块...")
        try:
            from app_loader import AppLoader
            print("✅ AppLoader导入成功")
            
            # 创建AppLoader
            loader = AppLoader()
            print("✅ AppLoader创建成功")
            
            # 检查模型是否存在
            from model_downloader import ModelDownloader
            downloader = ModelDownloader()
            missing_models = downloader.get_missing_models()
            
            if missing_models:
                print(f"📋 检测到缺失模型: {list(missing_models.keys())}")
                print("🎯 这应该会触发模型下载对话框")
                
                # 设置定时器自动退出
                def auto_exit():
                    print("⏰ 自动退出测试")
                    app.quit()
                
                timer = QTimer()
                timer.timeout.connect(auto_exit)
                timer.start(3000)  # 3秒后退出
                
                # 启动应用
                print("🚀 启动应用...")
                loader.start_loading()
                
                # 运行事件循环
                app.exec()
                print("✅ 应用正常退出")
                
            else:
                print("✅ 所有模型都存在，应用应该正常启动")
                app.quit()
                
        except Exception as e:
            print(f"❌ 主模块导入失败: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ PyQt6导入失败: {e}")
        return False
    finally:
        os.chdir(original_cwd)
    
    return True

def test_model_detection():
    """测试模型检测逻辑"""
    print("\n🔍 测试模型检测逻辑")
    
    app_resources = Path("Dou-flow.app/Contents/Resources")
    src_dir = app_resources / "src"
    
    if not src_dir.exists():
        print("❌ src目录不存在")
        return False
    
    # 切换到src目录
    original_cwd = os.getcwd()
    os.chdir(src_dir)
    
    if str(src_dir) not in sys.path:
        sys.path.insert(0, str(src_dir))
    
    try:
        from model_downloader import ModelDownloader
        
        downloader = ModelDownloader()
        missing_models = downloader.get_missing_models()
        
        print(f"📊 模型检测结果:")
        if missing_models:
            print(f"❌ 缺失模型: {list(missing_models.keys())}")
            for model_name, model_info in missing_models.items():
                print(f"   - {model_name}: {model_info.get('description', 'N/A')}")
        else:
            print("✅ 所有模型都存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型检测失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        os.chdir(original_cwd)

def main():
    """主函数"""
    print("🧪 Dou-flow GUI启动测试")
    print("=" * 50)
    
    # 检查应用包
    if not Path("Dou-flow.app").exists():
        print("❌ Dou-flow.app 不存在")
        return 1
    
    # 测试模型检测
    if not test_model_detection():
        print("❌ 模型检测测试失败")
        return 1
    
    # 测试GUI启动
    if not test_gui_startup():
        print("❌ GUI启动测试失败")
        return 1
    
    print("\n🎉 所有测试完成")
    return 0

if __name__ == "__main__":
    sys.exit(main())
