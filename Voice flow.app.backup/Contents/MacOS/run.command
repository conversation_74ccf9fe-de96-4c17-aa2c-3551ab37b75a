#!/bin/bash

# 获取脚本所在目录
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$DIR/../../../"

# 设置错误处理
set -e

# 显示用户友好的错误信息
show_error() {
    osascript -e "display dialog \"$1\" buttons {\"确定\"} default button \"确定\" with icon stop with title \"Dou-flow 启动错误\""
    exit 1
}

show_info() {
    osascript -e "display dialog \"$1\" buttons {\"确定\"} default button \"确定\" with icon note with title \"Dou-flow\""
}

# 检查并安装conda环境
check_and_install_conda() {
    # 设置conda路径
    export PATH="$HOME/miniconda3/bin:$PATH"
    
    # 初始化conda环境
    if [ -f "$HOME/miniconda3/etc/profile.d/conda.sh" ]; then
        source "$HOME/miniconda3/etc/profile.d/conda.sh"
    fi
    
    # 检查conda是否安装
    if [ ! -f "$HOME/miniconda3/bin/conda" ] && ! command -v conda >/dev/null 2>&1; then
        show_error "未检测到conda环境。请先安装Miniconda：

1. 访问 https://docs.conda.io/en/latest/miniconda.html
2. 下载适合您系统的版本
3. 安装后重新运行此应用"
        return 1
    fi

    # 检查wispr-flow-python311环境是否存在
    if ! conda env list | grep -q "wispr-flow-python311"; then
        show_info "首次运行需要安装依赖环境，这可能需要几分钟时间..."

        # 创建环境
        conda create -n wispr-flow-python311 python=3.11 -y || show_error "创建conda环境失败"

        # 激活环境并安装依赖
        eval "$(conda shell.bash hook)"
        conda activate wispr-flow-python311
        pip install -r requirements.txt || show_error "安装依赖失败"

        show_info "环境安装完成！"
    fi
}

# 主要逻辑
main() {
    # 检查并安装环境
    check_and_install_conda

    # 激活环境
    export PATH="$HOME/miniconda3/bin:$PATH"
    
    # 初始化conda环境
    if [ -f "$HOME/miniconda3/etc/profile.d/conda.sh" ]; then
        source "$HOME/miniconda3/etc/profile.d/conda.sh"
    fi
    
    # 激活wispr-flow-python311环境
    conda activate wispr-flow-python311 || show_error "无法激活conda环境 wispr-flow-python311"

    # 检查Python环境
    if ! command -v python >/dev/null 2>&1; then
        show_error "Python环境异常"
    fi

    # 运行程序
    python src/main.py 2>&1 | tee app_error.log
}

main "$@"
