#!/bin/bash

# 设置应用名称
APP_NAME="Voice flow"

echo "开始打包 ${APP_NAME}.app..."

# 切换到项目根目录
cd "$(dirname "$0")"

# 删除旧的应用包
if [ -d "${APP_NAME}.app" ]; then
    echo "删除旧的应用包..."
    rm -rf "${APP_NAME}.app"
fi

# 创建应用包结构
echo "创建应用包结构..."
mkdir -p "${APP_NAME}.app/Contents/"{MacOS,Resources}

# 创建 Info.plist
echo "创建 Info.plist..."
cat > "${APP_NAME}.app/Contents/Info.plist" << EOL
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>run.command</string>
    <key>CFBundleIconFile</key>
    <string>voice_flow_icon</string>
    <key>CFBundleIdentifier</key>
    <string>com.voiceflow.app</string>
    <key>CFBundleName</key>
    <string>${APP_NAME}</string>
    <key>CFBundleDisplayName</key>
    <string>Voice flow</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>V1.0</string>
    <key>CFBundleVersion</key>
    <string>1.0.0</string>
    <key>LSMinimumSystemVersion</key>
    <string>10.10</string>
    <key>NSMicrophoneUsageDescription</key>
    <string>需要使用麦克风来录制音频</string>
    <key>NSAppleEventsUsageDescription</key>
    <string>需要访问系统事件以支持快捷键和粘贴功能</string>
    <key>NSAppleEventsEnabled</key>
    <true/>
    <key>NSAccessibilityUsageDescription</key>
    <string>需要辅助功能权限来支持自动粘贴</string>
</dict>
</plist>
EOL

# 创建 entitlements.plist
echo "创建 entitlements.plist..."
cat > "${APP_NAME}.app/Contents/entitlements.plist" << EOL
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>com.apple.security.automation.apple-events</key>
    <true/>
    <key>com.apple.security.device.microphone</key>
    <true/>
    <key>com.apple.security.device.audio-input</key>
    <true/>
    <key>com.apple.security.cs.allow-jit</key>
    <true/>
    <key>com.apple.security.cs.allow-unsigned-executable-memory</key>
    <true/>
    <key>com.apple.security.cs.disable-library-validation</key>
    <true/>
    <key>com.apple.security.temporary-exception.apple-events</key>
    <array>
        <string>com.apple.systemevents</string>
        <string>com.apple.finder</string>
        <string>*</string>
    </array>
    <key>com.apple.security.files.user-selected.read-write</key>
    <true/>
</dict>
</plist>
EOL

# 创建启动脚本 - 使用 wispr-flow-python311 环境
echo "创建启动脚本..."
cat > "${APP_NAME}.app/Contents/MacOS/run.command" << 'EOL'
#!/bin/bash

# 获取脚本所在目录
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$DIR/../../../"

# 设置错误处理
set -e

# 设置环境变量
export DISABLE_INPUT_SOURCE_CHECK=1
export LAUNCHED_FROM_APP_BUNDLE=1
export PYTHONUNBUFFERED=1
export QT_MAC_DISABLE_FOREGROUND_APPLICATION_TRANSFORM=1

# 显示用户友好的错误信息
show_error() {
    osascript -e "display dialog \"$1\" buttons {\"确定\"} default button \"确定\" with icon stop with title \"Voice flow 启动错误\""
    exit 1
}

show_info() {
    osascript -e "display dialog \"$1\" buttons {\"确定\"} default button \"确定\" with icon note with title \"Voice flow\""
}

# 检查并安装conda环境
check_and_install_conda() {
    # 设置conda路径
    export PATH="$HOME/miniconda3/bin:$PATH"
    
    # 初始化conda环境
    if [ -f "$HOME/miniconda3/etc/profile.d/conda.sh" ]; then
        source "$HOME/miniconda3/etc/profile.d/conda.sh"
    fi
    
    # 检查conda是否安装
    if [ ! -f "$HOME/miniconda3/bin/conda" ] && ! command -v conda >/dev/null 2>&1; then
        show_error "未检测到conda环境。请先安装Miniconda：

1. 访问 https://docs.conda.io/en/latest/miniconda.html
2. 下载适合您系统的版本
3. 安装后重新运行此应用"
        return 1
    fi

    # 检查wispr-flow-python311环境是否存在
    if ! conda env list | grep -q "wispr-flow-python311"; then
        show_info "首次运行需要安装依赖环境，这可能需要几分钟时间..."

        # 创建环境
        conda create -n wispr-flow-python311 python=3.11 -y || show_error "创建conda环境失败"

        # 激活环境并安装依赖
        eval "$(conda shell.bash hook)"
        conda activate wispr-flow-python311
        pip install -r requirements.txt || show_error "安装依赖失败"

        show_info "环境安装完成！"
    fi
}

# 主要逻辑
main() {
    # 检查并安装环境
    check_and_install_conda

    # 激活环境
    export PATH="$HOME/miniconda3/bin:$PATH"
    
    # 初始化conda环境
    if [ -f "$HOME/miniconda3/etc/profile.d/conda.sh" ]; then
        source "$HOME/miniconda3/etc/profile.d/conda.sh"
    fi
    
    # 激活wispr-flow-python311环境
    conda activate wispr-flow-python311 || show_error "无法激活conda环境 wispr-flow-python311"

    # 检查Python环境
    if ! command -v python >/dev/null 2>&1; then
        show_error "Python环境异常"
    fi

    # 运行程序
    python src/main.py 2>&1 | tee app_error.log
}

main "$@"
EOL

# 设置执行权限
echo "设置执行权限..."
chmod +x "${APP_NAME}.app/Contents/MacOS/run.command"

# 复制新图标
echo "复制图标..."
if [ -f "voice_flow_icon.icns" ]; then
    cp "voice_flow_icon.icns" "${APP_NAME}.app/Contents/Resources/"
else
    echo "警告：未找到 voice_flow_icon.icns 文件"
fi

# 复制所有源代码和资源
echo "复制源代码和资源..."
cp -r src "${APP_NAME}.app/Contents/Resources/"
cp -r resources "${APP_NAME}.app/Contents/Resources/"

# 移除扩展属性
echo "移除扩展属性..."
xattr -cr "${APP_NAME}.app"

# 对应用进行签名
echo "对应用进行签名..."
codesign --force --deep --sign - --entitlements "${APP_NAME}.app/Contents/entitlements.plist" "${APP_NAME}.app"

echo ""
echo "✅ 打包完成！"
echo "📦 应用位置: ${APP_NAME}.app"
echo "🚀 现在可以双击 ${APP_NAME}.app 启动应用"
echo ""
echo "注意：应用将使用 wispr-flow-python311 conda 环境"