# Dou-flow v1.0.0 DMG 发布包

## 📦 发布信息

- **版本**: v1.0.0
- **发布日期**: 2025-08-05
- **文件名**: `Dou-flow-1.0.0.dmg`
- **文件大小**: 856KB
- **支持系统**: macOS 10.15+ (Intel & Apple Silicon)

## 🎯 主要特性

### 核心功能
- **实时语音转文字**: 按住快捷键录音，松开自动转换并粘贴
- **智能标点符号**: 自动添加标点符号，提高文本可读性
- **自定义快捷键**: 支持 Fn、Ctrl、Alt 等多种快捷键组合
- **热词支持**: 添加专业术语提高识别准确率
- **多语言支持**: 主要支持中文，兼容英文

### 技术特性
- **离线运行**: AI模型本地运行，保护隐私
- **自动环境配置**: 智能检测并配置Python环境
- **模型按需下载**: 首次使用时下载，减少初始安装包大小
- **跨架构支持**: 同时支持Intel和Apple Silicon Mac

## 🔧 安装要求

### 系统要求
- macOS 10.15 (Catalina) 或更高版本
- 至少 4GB 可用存储空间
- 稳定的网络连接（首次运行时下载模型）

### 可选依赖
- Miniconda 或 Anaconda（应用会自动检测，如无则提示安装）

## 🚀 安装步骤

### 1. 下载和挂载
```bash
# 下载DMG文件
curl -O https://github.com/ttmouse/Wispr-Flow-CN/releases/download/v1.0.0/Dou-flow-1.0.0.dmg

# 双击挂载DMG文件
open Dou-flow-1.0.0.dmg
```

### 2. 安装应用
1. 将 `Dou-flow.app` 拖拽到 `Applications` 文件夹
2. 右键点击应用选择"打开"（绕过安全检查）
3. 按照提示授予必要权限

### 3. 首次运行配置
应用会自动进行以下配置：
- 检测Python环境（如无则提示安装Conda）
- 创建专用虚拟环境 `wispr-flow-python311`
- 安装所需依赖包
- 下载AI模型（约1.3GB，需要网络连接）

## 📋 权限说明

应用需要以下权限才能正常工作：

### 必需权限
- **麦克风权限**: 录制音频进行语音识别
- **辅助功能权限**: 支持全局快捷键和自动粘贴
- **Apple Events权限**: 系统事件访问

### 权限配置
1. 打开"系统偏好设置" → "安全性与隐私"
2. 在"隐私"标签页中配置：
   - 麦克风：勾选 Dou-flow
   - 辅助功能：勾选 Dou-flow
   - 自动化：允许 Dou-flow 控制其他应用

## 🎤 使用方法

### 基本操作
1. **开始录音**: 按住 Fn 键（或自定义快捷键）
2. **说话**: 清晰地说出要转换的内容
3. **结束录音**: 松开快捷键
4. **自动粘贴**: 转换后的文本自动粘贴到当前应用

### 高级设置
- **快捷键自定义**: 在设置中更改录音快捷键
- **热词管理**: 添加专业术语提高识别率
- **音频调节**: 调整音量阈值和录音时长
- **输出格式**: 自定义文本格式和标点符号

## 🔍 故障排除

### 常见问题

#### 1. 应用无法启动
**症状**: 双击应用无反应或显示错误
**解决方案**:
- 确保已授予所有必需权限
- 检查是否安装了Conda环境
- 查看错误日志：`~/dou-flow-error.log`

#### 2. 模型下载失败
**症状**: 显示"您选择了取消下载"或下载错误
**解决方案**:
- 检查网络连接
- 重新启动应用，会显示模型下载对话框
- 手动重试下载

#### 3. 语音识别不准确
**症状**: 转换的文本错误较多
**解决方案**:
- 确保麦克风工作正常
- 在安静环境中使用
- 添加专业术语到热词列表
- 调整音频设置中的音量阈值

#### 4. 快捷键不响应
**症状**: 按快捷键无法开始录音
**解决方案**:
- 检查辅助功能权限
- 确认快捷键没有与其他应用冲突
- 尝试更换其他快捷键

### 日志文件
- **应用日志**: `~/dou-flow-error.log`
- **详细日志**: `~/Library/Logs/Dou-flow/`

## 📞 技术支持

### 获取帮助
- **GitHub Issues**: https://github.com/ttmouse/Wispr-Flow-CN/issues
- **文档**: 查看DMG中的安装说明和快速开始指南
- **日志分析**: 提交issue时请附上相关日志文件

### 反馈渠道
- 功能建议和bug报告请通过GitHub Issues提交
- 包含详细的系统信息和错误日志
- 描述重现步骤和预期行为

## 🔄 更新说明

### v1.0.0 新特性
- ✅ 智能模型下载检测
- ✅ 友好的用户界面
- ✅ 优化的错误处理
- ✅ 自动环境配置
- ✅ 跨架构支持

### 已修复问题
- 修复了"您选择了取消下载"的错误提示
- 改进了模型缺失时的用户体验
- 优化了应用启动流程
- 增强了错误处理机制

## 📄 许可证

本软件遵循 MIT 许可证，详见项目根目录的 LICENSE 文件。

## 🙏 致谢

感谢所有贡献者和测试用户的支持，特别是：
- FunASR 团队提供的语音识别模型
- ModelScope 平台的模型托管服务
- 社区用户的反馈和建议

---

**开发者**: ttmouse  
**项目地址**: https://github.com/ttmouse/Wispr-Flow-CN  
**发布时间**: 2025-08-05
