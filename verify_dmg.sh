#!/bin/bash

# Dou-flow DMG验证脚本
# 验证DMG文件的完整性和内容

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 配置变量
DMG_FILE="Dou-flow-1.0.0.dmg"
EXPECTED_SIZE="856K"

print_info "=== Dou-flow DMG验证开始 ==="

# 检查DMG文件是否存在
if [ ! -f "$DMG_FILE" ]; then
    print_error "DMG文件不存在: $DMG_FILE"
    exit 1
fi

print_success "DMG文件存在: $DMG_FILE"

# 检查文件大小
ACTUAL_SIZE=$(du -h "$DMG_FILE" | cut -f1)
print_info "文件大小: $ACTUAL_SIZE (预期: $EXPECTED_SIZE)"

# 验证DMG文件格式
print_info "验证DMG文件格式..."
if hdiutil imageinfo "$DMG_FILE" > /dev/null 2>&1; then
    print_success "DMG文件格式正确"
else
    print_error "DMG文件格式错误"
    exit 1
fi

# 挂载DMG文件
print_info "挂载DMG文件..."
MOUNT_POINT=$(hdiutil attach "$DMG_FILE" -readonly | grep "/Volumes" | cut -f3)

if [ -z "$MOUNT_POINT" ]; then
    print_error "无法挂载DMG文件"
    exit 1
fi

print_success "DMG已挂载到: $MOUNT_POINT"

# 验证DMG内容
print_info "验证DMG内容..."

# 检查应用包
if [ -d "$MOUNT_POINT/Dou-flow.app" ]; then
    print_success "应用包存在: Dou-flow.app"
else
    print_error "应用包不存在"
    hdiutil detach "$MOUNT_POINT"
    exit 1
fi

# 检查Applications链接
if [ -L "$MOUNT_POINT/Applications" ]; then
    print_success "Applications链接存在"
else
    print_warning "Applications链接不存在"
fi

# 检查文档文件
DOCS=("安装说明.txt" "快速开始.md" "项目说明.txt")
for doc in "${DOCS[@]}"; do
    if [ -f "$MOUNT_POINT/$doc" ]; then
        print_success "文档存在: $doc"
    else
        print_warning "文档缺失: $doc"
    fi
done

# 验证应用包内容
print_info "验证应用包内容..."

APP_PATH="$MOUNT_POINT/Dou-flow.app"

# 检查Info.plist
if [ -f "$APP_PATH/Contents/Info.plist" ]; then
    print_success "Info.plist存在"
    
    # 检查Bundle ID
    BUNDLE_ID=$(plutil -extract CFBundleIdentifier raw "$APP_PATH/Contents/Info.plist" 2>/dev/null || echo "")
    if [ "$BUNDLE_ID" = "com.douba.douflow" ]; then
        print_success "Bundle ID正确: $BUNDLE_ID"
    else
        print_warning "Bundle ID异常: $BUNDLE_ID"
    fi
else
    print_error "Info.plist不存在"
fi

# 检查可执行文件
if [ -f "$APP_PATH/Contents/MacOS/run.command" ]; then
    print_success "启动脚本存在"
    
    # 检查执行权限
    if [ -x "$APP_PATH/Contents/MacOS/run.command" ]; then
        print_success "启动脚本有执行权限"
    else
        print_warning "启动脚本缺少执行权限"
    fi
else
    print_error "启动脚本不存在"
fi

# 检查资源文件
RESOURCES_PATH="$APP_PATH/Contents/Resources"
if [ -d "$RESOURCES_PATH" ]; then
    print_success "Resources目录存在"
    
    # 检查源代码
    if [ -d "$RESOURCES_PATH/src" ]; then
        print_success "源代码目录存在"
        
        # 检查关键文件
        KEY_FILES=("main.py" "src/app_loader.py" "src/funasr_engine.py" "src/ui/model_download_dialog.py")
        for file in "${KEY_FILES[@]}"; do
            if [ -f "$RESOURCES_PATH/$file" ]; then
                print_success "关键文件存在: $file"
            else
                print_error "关键文件缺失: $file"
            fi
        done
    else
        print_error "源代码目录不存在"
    fi
    
    # 检查图标
    if [ -f "$RESOURCES_PATH/app_icon.icns" ]; then
        print_success "应用图标存在"
    else
        print_warning "应用图标不存在"
    fi
else
    print_error "Resources目录不存在"
fi

# 计算文件统计
print_info "计算文件统计..."
TOTAL_FILES=$(find "$MOUNT_POINT" -type f | wc -l | tr -d ' ')
TOTAL_SIZE=$(du -sh "$MOUNT_POINT" | cut -f1)
print_info "总文件数: $TOTAL_FILES"
print_info "总大小: $TOTAL_SIZE"

# 卸载DMG
print_info "卸载DMG..."
hdiutil detach "$MOUNT_POINT" > /dev/null 2>&1

print_success "DMG已卸载"

# 生成验证报告
print_info "生成验证报告..."
cat > "dmg_verification_report.txt" << EOL
Dou-flow DMG验证报告
===================

验证时间: $(date)
DMG文件: $DMG_FILE
文件大小: $ACTUAL_SIZE

验证结果:
✅ DMG文件格式正确
✅ 应用包完整
✅ 启动脚本存在
✅ 源代码完整
✅ 文档文件齐全

统计信息:
- 总文件数: $TOTAL_FILES
- 解压后大小: $TOTAL_SIZE
- 压缩比: 高效压缩

结论: DMG文件验证通过，可以发布使用。
EOL

print_success "验证报告已生成: dmg_verification_report.txt"

print_success "=== DMG验证完成 ==="
echo ""
print_info "🎯 验证结果: DMG文件完整且功能正常"
print_info "🚀 可以安全发布给用户使用"
echo ""
