"""
安全的热键管理器 - 专为打包应用设计
避免所有可能导致崩溃的系统API调用
"""

import os
import logging
import threading
import time

# 处理导入问题，支持打包环境
try:
    from .hotkey_manager_base import HotkeyManagerBase
except ImportError:
    from hotkey_manager_base import HotkeyManagerBase

class SafeHotkeyManager(HotkeyManagerBase):
    """安全的热键管理器 - 为打包环境设计"""
    
    def __init__(self, settings_manager=None):
        super().__init__(settings_manager)
        self.settings_manager = settings_manager
        self.logger = logging.getLogger('SafeHotkeyManager')
        self.is_running = False
        self._stop_event = threading.Event()
        
        # 简化的状态管理
        self.recording = False
        self.last_trigger_time = 0
        
        self.logger.info("安全热键管理器已初始化（打包模式）")
    
    def start_listening(self):
        """启动安全的热键监听"""
        try:
            self.is_running = True
            self._stop_event.clear()
            
            # 启动一个简单的监听线程，不调用任何系统API
            self.listener_thread = threading.Thread(target=self._safe_listener)
            self.listener_thread.daemon = True
            self.listener_thread.start()
            
            self.logger.info("安全热键监听器已启动")
        except Exception as e:
            self.logger.error(f"启动安全热键监听器失败: {e}")
    
    def _safe_listener(self):
        """安全的监听循环 - 使用安全的按键检测方法"""
        self.logger.info("安全监听线程已启动")
        
        # 用于检测fn键状态的变量
        last_fn_state = False
        arrow_key_blocked_until = 0  # 箭头键阻塞截止时间
        
        while self.is_running and not self._stop_event.is_set():
            try:
                current_time = time.time()
                
                # 检查是否有箭头键被按下
                if self._is_arrow_key_pressed():
                    # 箭头键被按下时，设置较长的阻塞时间(200ms)
                    arrow_key_blocked_until = current_time + 0.2
                    time.sleep(0.05)
                    continue
                
                # 如果仍在箭头键阻塞期间，继续跳过
                if current_time < arrow_key_blocked_until:
                    self.logger.debug("仍在箭头键阻塞期间，跳过检测")
                    time.sleep(0.05)
                    continue
                
                # 使用更安全的方式检测fn键状态
                current_fn_state = self._safe_check_fn_key()
                
                # 只在状态真正改变时处理
                if current_fn_state != last_fn_state:
                    self.logger.info(f"状态变化: {last_fn_state} -> {current_fn_state}")
                    
                    # 检测fn键按下（且无箭头键干扰）
                    if current_fn_state and not last_fn_state:
                        if not self.recording:
                            self.logger.info("状态变化触发: fn键按下，开始录音")
                            self.trigger_recording_start()
                    
                    # 检测fn键释放
                    elif not current_fn_state and last_fn_state:
                        if self.recording:
                            self.logger.info("状态变化触发: fn键释放，停止录音")
                            self.trigger_recording_stop()
                    
                    last_fn_state = current_fn_state
                time.sleep(0.05)  # 减少检测间隔提高响应性
                
            except Exception as e:
                self.logger.error(f"安全监听循环错误: {e}")
                time.sleep(0.1)
        
        self.logger.info("安全监听线程已退出")
    
    def _is_arrow_key_pressed(self):
        """检查是否有箭头键被按下"""
        try:
            import Quartz
            arrow_keycodes = [123, 124, 125, 126]  # 左，右，下，上箭头
            for keycode in arrow_keycodes:
                if Quartz.CGEventSourceKeyState(Quartz.kCGEventSourceStateCombinedSessionState, keycode):
                    self.logger.info(f"检测到箭头键{keycode}，启动200ms阻塞期")
                    return True
            return False
        except Exception as e:
            self.logger.error(f"箭头键检测失败: {e}")
            return False
        
    def _safe_check_fn_key(self):
        """简化版fn键检测 - 箭头键已在外层过滤"""
        try:
            import Quartz
            
            # 检查fn键状态
            flags = Quartz.CGEventSourceFlagsState(Quartz.kCGEventSourceStateCombinedSessionState)
            fn_mask = 1 << 23
            fn_pressed = bool(flags & fn_mask)
            
            # 记录状态变化用于调试
            if fn_pressed != getattr(self, '_last_valid_fn_state', None):
                self.logger.info(f"fn键状态变化: {getattr(self, '_last_valid_fn_state', None)} -> {fn_pressed}")
            
            self._last_valid_fn_state = fn_pressed
            return fn_pressed
            
        except Exception as e:
            self.logger.error(f"fn键检测失败: {e}")
            return False
    
    def stop_listening(self):
        """停止热键监听"""
        try:
            self.is_running = False
            self._stop_event.set()
            
            if hasattr(self, 'listener_thread') and self.listener_thread.is_alive():
                self.listener_thread.join(timeout=1.0)
            
            self.logger.info("安全热键监听器已停止")
        except Exception as e:
            self.logger.error(f"停止安全热键监听器失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        self.stop_listening()
        self.logger.info("安全热键管理器已清理")
    
    def is_listening(self):
        """检查是否正在监听"""
        return self.is_running
    
    def get_manager_type(self):
        """返回管理器类型"""
        return "safe"
    
    def trigger_recording_start(self):
        """触发录音开始"""
        if hasattr(self, 'press_callback') and self.press_callback:
            try:
                self.press_callback()
                self.recording = True
                self.last_trigger_time = time.time()
                self.logger.info("触发录音开始")
            except Exception as e:
                self.logger.error(f"触发录音开始失败: {e}")
    
    def trigger_recording_stop(self):
        """触发录音停止"""
        if hasattr(self, 'release_callback') and self.release_callback:
            try:
                self.release_callback()
                self.recording = False
                self.logger.info("触发录音停止")
            except Exception as e:
                self.logger.error(f"触发录音停止失败: {e}")
    
    def update_hotkey(self, hotkey):
        """更新热键设置 - 安全版本，不执行实际操作"""
        self.logger.info(f"安全模式：忽略热键更新 {hotkey}")
    
    def update_delay_settings(self):
        """更新延迟设置 - 安全版本，不执行实际操作"""
        self.logger.info("安全模式：忽略延迟设置更新")
    
    def get_status(self):
        """获取热键管理器状态"""
        return {
            'type': 'safe',
            'running': self.is_running,
            'recording': self.recording,
            'safe_mode': True
        }
    
    def reset_state(self):
        """重置状态 - 安全版本"""
        try:
            self.recording = False
            self.last_trigger_time = 0
            self.logger.info("安全模式：状态已重置")
        except Exception as e:
            self.logger.error(f"重置状态失败: {e}")
    
    def force_reset(self):
        """强制重置 - 安全版本"""
        try:
            self.stop_listening()
            self.reset_state()
            self.logger.info("安全模式：强制重置完成")
        except Exception as e:
            self.logger.error(f"强制重置失败: {e}")