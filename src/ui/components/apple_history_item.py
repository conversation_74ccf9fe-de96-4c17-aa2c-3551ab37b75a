# Apple风格的历史记录项组件
# 包含文本内容、时间戳和复制按钮

from PyQt6.QtWidgets import QWidget, QHBoxLayout, QVBoxLayout, QLabel, QPushButton, QGraphicsDropShadowEffect
from PyQt6.QtCore import Qt, QSize, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import QFont, QCursor, QPixmap, QIcon, QColor
from datetime import datetime
import pyperclip
import os

class AppleHistoryItemWidget(QWidget):
    """Apple风格的历史记录项Widget
    
    包含：
    - 主要文本内容
    - 时间戳显示
    - 复制按钮
    """
    
    # 定义信号
    copy_clicked = pyqtSignal(str)  # 复制按钮点击信号
    
    def __init__(self, text, timestamp=None, parent=None):
        super().__init__(parent)
        self.original_text = text
        self.timestamp = timestamp or datetime.now().isoformat()
        self._setup_ui()
        self._apply_apple_style()
        
    def _setup_ui(self):
        """设置UI布局"""
        # 主布局 - 垂直布局，紧凑化设计
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(16, 12, 16, 12)  # 减少边距，更紧凑
        main_layout.setSpacing(6)  # 减少组件间距
        
        # 上部分 - 文本和复制按钮
        top_layout = QHBoxLayout()
        top_layout.setSpacing(12)  # 适中的间距
        
        # 文本区域
        text_container = QVBoxLayout()
        text_container.setSpacing(3)  # 减少文本和时间戳之间的间距
        
        # 主文本标签 - 优化字体和间距确保文字不被切断
        self.text_label = QLabel(self._clean_html(self.original_text))
        self.text_label.setWordWrap(True)
        self.text_label.setTextFormat(Qt.TextFormat.PlainText)
        font = QFont(".AppleSystemUIFont", 14)
        font.setHintingPreference(QFont.HintingPreference.PreferDefaultHinting)
        self.text_label.setFont(font)
        self.text_label.setContentsMargins(0, 6, 0, 6)  # 增加上下边距确保文字显示完整
        self.text_label.setStyleSheet("""
            QLabel {
                line-height: 1.6;
                padding: 6px 0px;
                min-height: 24px;
            }
        """)
        text_container.addWidget(self.text_label)
        
        # 时间戳标签 - 优化字体大小
        self.timestamp_label = QLabel(self._format_timestamp(self.timestamp))
        timestamp_font = QFont(".AppleSystemUIFont", 11)  # 稍微减小时间戳字号
        self.timestamp_label.setFont(timestamp_font)
        self.timestamp_label.setContentsMargins(0, 1, 0, 1)  # 适中边距
        self.timestamp_label.setStyleSheet("color: #8E8E93; padding: 1px 0px; min-height: 17px;")
        text_container.addWidget(self.timestamp_label)
        
        top_layout.addLayout(text_container, 1)
        
        # 复制按钮 - 使用图片图标和现代化设计
        self.copy_button = QPushButton()
        self.copy_button.setFixedSize(36, 36)  # 增大按钮尺寸
        self.copy_button.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.copy_button.clicked.connect(self._on_copy_clicked)
        self.copy_button.setToolTip("复制到剪贴板")
        
        # 设置图片图标
        icon_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "resources", "paste_icon.png")
        if os.path.exists(icon_path):
            icon = QIcon(icon_path)
            self.copy_button.setIcon(icon)
            self.copy_button.setIconSize(QSize(20, 20))  # 图标大小
        
        # 现代化按钮样式 - 采用扁平设计和微妙阴影
        self.copy_button.setStyleSheet("""
            QPushButton {
                background-color: #F8F9FA;
                border: none;
                border-radius: 18px;
                font-size: 14px;
                font-weight: 500;
                color: #6C757D;
            }
            QPushButton:hover {
                background-color: #E3F2FD;
            }
            QPushButton:pressed {
                background-color: #BBDEFB;
            }
        """)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(8)
        shadow.setColor(QColor(0, 0, 0, 30))
        shadow.setOffset(0, 2)
        self.copy_button.setGraphicsEffect(shadow)
        
        top_layout.addWidget(self.copy_button, 0, Qt.AlignmentFlag.AlignTop)
        
        main_layout.addLayout(top_layout)
        
        # 现代化分隔线
        separator = QWidget()
        separator.setFixedHeight(1)
        separator.setStyleSheet("""
            background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0, 
                                      stop: 0 transparent, 
                                      stop: 0.1 #E0E0E0, 
                                      stop: 0.9 #E0E0E0, 
                                      stop: 1 transparent);
        """)
        main_layout.addWidget(separator)
        
    def _apply_apple_style(self):
        """应用现代化Apple设计风格"""
        # 添加整体阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(12)
        shadow.setColor(QColor(0, 0, 0, 15))
        shadow.setOffset(0, 4)
        self.setGraphicsEffect(shadow)
        
        self.setStyleSheet("""
            AppleHistoryItemWidget {
                background-color: #FFFFFF;
                border-radius: 12px;
                border: 1px solid rgba(0, 0, 0, 0.06);
            }
            AppleHistoryItemWidget:hover {
                background-color: #FAFBFC;
                border: 1px solid rgba(0, 122, 255, 0.2);
            }
        """)
        
    def _clean_html(self, text):
        """清理HTML标签"""
        import re
        # 移除HTML标签
        clean_text = re.sub(r'<[^>]+>', '', text)
        return clean_text.strip()
        
    def _format_timestamp(self, timestamp_str):
        """格式化时间戳显示"""
        try:
            # 解析ISO格式时间戳
            dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            now = datetime.now()
            
            # 计算时间差
            diff = now - dt
            
            # 根据时间差显示不同格式
            if diff.days == 0:
                if diff.seconds < 60:
                    return "刚刚"
                elif diff.seconds < 3600:
                    minutes = diff.seconds // 60
                    return f"{minutes}分钟前"
                else:
                    hours = diff.seconds // 3600
                    return f"{hours}小时前"
            elif diff.days == 1:
                return "昨天 " + dt.strftime("%H:%M")
            elif diff.days < 7:
                return f"{diff.days}天前"
            else:
                # 显示完整日期
                return dt.strftime("%Y年%m月%d日 %H:%M")
                
        except Exception:
            # 如果解析失败，返回原始字符串
            return timestamp_str
            
    def _on_copy_clicked(self):
        """处理复制按钮点击"""
        clean_text = self._clean_html(self.original_text)
        try:
            pyperclip.copy(clean_text)
            # 临时改变按钮样式表示复制成功 - 现代化设计
            original_icon = self.copy_button.icon()
            
            # 创建成功状态的绿色背景样式
            self.copy_button.setStyleSheet("""
                QPushButton {
                    background-color: #4CAF50;
                    border: none;
                    border-radius: 18px;
                    font-size: 16px;
                    font-weight: 600;
                    color: white;
                }
            """)
            
            # 设置成功图标（使用✓符号）
            self.copy_button.setIcon(QIcon())  # 清除原图标
            self.copy_button.setText("✓")
            
            # 1秒后恢复原样
            QTimer.singleShot(1000, lambda: self._restore_copy_button(original_icon))
            
            # 发射信号
            self.copy_clicked.emit(clean_text)
            
        except Exception as e:
            import logging
            logging.error(f"复制失败: {e}")
            
    def _restore_copy_button(self, original_icon):
        """恢复复制按钮原样"""
        self.copy_button.setText("")  # 清除文本
        self.copy_button.setIcon(original_icon)  # 恢复原图标
        
        # 恢复原始现代化样式
        self.copy_button.setStyleSheet("""
            QPushButton {
                background-color: #F8F9FA;
                border: none;
                border-radius: 18px;
                font-size: 14px;
                font-weight: 500;
                color: #6C757D;
            }
            QPushButton:hover {
                background-color: #E3F2FD;
            }
            QPushButton:pressed {
                background-color: #BBDEFB;
            }
        """)
        
    def getText(self):
        """获取原始文本内容"""
        return self.original_text
        
    def getTimestamp(self):
        """获取时间戳"""
        return self.timestamp
        
    def setText(self, text):
        """设置文本内容"""
        self.original_text = text
        self.text_label.setText(self._clean_html(text))
        
    def setTimestamp(self, timestamp):
        """设置时间戳"""
        self.timestamp = timestamp
        self.timestamp_label.setText(self._format_timestamp(timestamp))
        
    def sizeHint(self):
        """返回建议大小 - 优化的统一高度计算规则"""
        # 获取实际的文本内容和长度
        text_content = self.original_text.strip()
        text_length = len(text_content)
        
        # 更精确的行数计算 - 基于实际文本测量
        # 考虑窗口宽度960px，去除边距和按钮后约760px可用
        available_width = 760  
        
        # 使用QFontMetrics进行精确测量
        from PyQt6.QtGui import QFontMetrics
        font = QFont(".AppleSystemUIFont", 14)
        font_metrics = QFontMetrics(font)
        
        # 分行计算实际行数
        words = text_content.split()
        if not words:
            estimated_lines = 1
        else:
            current_line_width = 0
            estimated_lines = 1
            space_width = font_metrics.horizontalAdvance(' ')
            
            for word in words:
                word_width = font_metrics.horizontalAdvance(word)
                if current_line_width + word_width + space_width > available_width:
                    estimated_lines += 1
                    current_line_width = word_width
                else:
                    current_line_width += word_width + space_width
        
        # 统一的高度规则 - 确保所有文本都有足够的显示空间
        # 基础高度组成（保证文字不被切断）：
        base_margin = 32    # 上下边距 (16+16)
        text_padding = 12   # 文本区域内边距 (6+6) 
        timestamp_height = 24  # 时间戳区域高度
        spacing = 12        # 组件间距 (6+3+3)
        separator_height = 8   # 分隔线区域
        
        fixed_height = base_margin + text_padding + timestamp_height + spacing + separator_height  # = 88px
        
        # 每行文本高度 - 增加行间距确保文字不被切断
        line_height = 24  # 增加到24px确保14px字体有足够空间
        text_area_height = estimated_lines * line_height
        
        # 计算总高度
        total_height = fixed_height + text_area_height
        
        # 统一的高度范围 - 确保一致性
        min_height = 90    # 提高最小高度确保单行文字显示完整
        max_height = 220   # 适当提高最大高度
        
        final_height = max(min_height, min(total_height, max_height))
        
        return QSize(900, final_height)