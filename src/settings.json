{"hotkey": "fn", "hotkey_scheme": "hammerspoon", "high_frequency_words": [], "audio": {"input_device": null, "volume_threshold": 150, "max_recording_duration": 10}, "asr": {"model_path": "", "punc_model_path": "", "auto_punctuation": true, "real_time_display": true, "hotword_weight": 80, "enable_pronunciation_correction": true}, "hotkey_settings": {"recording_start_delay": 50}, "paste": {"transcription_delay": 0, "history_click_delay": 0}, "cache": {"permissions": {"last_check": "", "accessibility": false, "microphone": false, "check_interval_hours": 24}, "models": {"last_check": "", "asr_available": false, "punc_available": false, "check_interval_hours": 168}}}