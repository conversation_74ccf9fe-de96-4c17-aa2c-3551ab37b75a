import sounddevice as sd
import numpy as np
import time
import collections
import queue
import threading

class AudioCapture:
    def __init__(self):
        # 使用deque限制缓冲区大小，避免内存累积
        self.frames = collections.deque(maxlen=1000)  # 限制最大帧数
        self.stream = None
        self.device_index = None
        self.read_count = 0
        # 音量相关参数
        self.volume_threshold = 0.001  # 降低默认阈值提高敏感度
        self.min_valid_frames = 2      # 降低最少有效帧数要求（约0.13秒）
        self.valid_frame_count = 0     # 有效音频帧计数
        self.max_silence_frames = 50   # 增加最大静音帧数到约2秒
        self.silence_frame_count = 0   # 连续静音帧计数
        self.debug_frame_count = 0     # 调试帧计数
        
        # 用于音频数据的队列
        self.audio_queue = queue.Queue()
        self.recording = False
        
        # 初始化音频系统
        self._initialize_audio()
        
    def _initialize_audio(self):
        """初始化音频系统"""
        try:
            # 获取默认输入设备
            self.device_index = sd.default.device[0]  # 输入设备索引
            if self.device_index is None:
                # 尝试查询默认输入设备
                default_input = sd.query_devices(kind='input')
                if default_input:
                    self.device_index = default_input['index']
        except Exception as e:
            import logging
            logging.error(f"初始化音频系统失败: {e}")
            self.device_index = None
        
    def _get_default_mic_index(self):
        """获取默认麦克风索引"""
        try:
            default_device = sd.query_devices(kind='input')
            return default_device['index'] if default_device else None
        except Exception as e:
            import logging
            logging.error(f"获取默认麦克风失败: {e}")
            return None

    def _audio_callback(self, indata, frames, time_info, status):
        """音频回调函数"""
        if status:
            import logging
            logging.warning(f"音频状态: {status}")
        
        if self.recording:
            # 复制数据并添加到帧缓冲区
            audio_data = indata.copy()
            self.frames.append(audio_data.tobytes())
            
            # 检查音量
            if self._is_valid_audio(audio_data.tobytes()):
                self.valid_frame_count += 1
                self.silence_frame_count = 0
            else:
                self.silence_frame_count += 1
                # 修复: 在按键录音模式下不应该因为静音而自动停止录音
                # 只有在明确释放热键时才停止录音，静音检测仅用于音量统计
                # 注释掉自动停止逻辑，让热键控制录音的开始和结束
                # if self.silence_frame_count > self.max_silence_frames and self.valid_frame_count > self.min_valid_frames:
                #     self.recording = False

    def start_recording(self):
        """开始录音"""
        retry_count = 0
        max_retries = 3
        
        while retry_count < max_retries:
            try:
                # 确保之前的录音已经停止
                self.stop_recording()
                
                if self.device_index is None:
                    self._initialize_audio()
                
                if self.device_index is None:
                    raise Exception("音频系统未正确初始化")
                
                self.frames.clear()
                self.read_count = 0
                self.valid_frame_count = 0
                self.silence_frame_count = 0
                self.debug_frame_count = 0
                self.recording = True
                
                # 创建音频流
                self.stream = sd.InputStream(
                    device=self.device_index,
                    channels=1,
                    samplerate=16000,
                    blocksize=512,
                    dtype=np.float32,
                    callback=self._audio_callback
                )
                
                self.stream.start()
                return
                
            except Exception as e:
                retry_count += 1
                import logging
                logging.error(f"尝试 {retry_count}/{max_retries} 启动录音失败: {e}")
                self._cleanup()
                time.sleep(0.5)  # 等待系统资源释放
        
        raise Exception(f"在 {max_retries} 次尝试后仍无法启动录音")

    def stop_recording(self):
        """停止录音"""
        self.recording = False
        
        if not self.stream:
            return np.array([], dtype=np.float32)

        try:
            if self.stream and self.stream.active:
                self.stream.stop()
                self.stream.close()
        except Exception as e:
            import logging
            logging.error(f"停止录音失败: {e}")
            # 强制重新初始化音频系统
            self._cleanup()
            self._initialize_audio()
        finally:
            self.stream = None
            
        try:
            audio_data = b"".join(self.frames)
            
            # 确保音频数据不为空
            if len(audio_data) == 0:
                return np.array([], dtype=np.float32)
                
            data = np.frombuffer(audio_data, dtype=np.float32)
            
            # 检查是否有足够的有效音频
            # 确保使用标量值进行比较，避免NumPy数组比较错误
            try:
                # 强制转换为Python标量类型，避免NumPy数组比较
                if hasattr(self.valid_frame_count, 'item'):
                    valid_count = int(self.valid_frame_count.item())
                elif hasattr(self.valid_frame_count, '__len__'):
                    valid_count = int(self.valid_frame_count)
                else:
                    valid_count = int(self.valid_frame_count)
                    
                if hasattr(self.min_valid_frames, 'item'):
                    min_valid = int(self.min_valid_frames.item())
                elif hasattr(self.min_valid_frames, '__len__'):
                    min_valid = int(self.min_valid_frames)
                else:
                    min_valid = int(self.min_valid_frames)
                    
                # 使用Python标量进行比较
                if valid_count < min_valid:
                    return np.array([], dtype=np.float32)
            except Exception as e:
                # 如果比较失败，返回原始数据
                pass
                
            return data
            
        except Exception as e:
            import logging
            logging.error(f"stop_recording处理音频数据时出错: {e}")
            return np.array([], dtype=np.float32)

    def _cleanup(self):
        """清理音频资源"""
        self.recording = False
        
        # 清理音频流
        if self.stream:
            try:
                if self.stream.active:
                    self.stream.stop()
                self.stream.close()
            except Exception as e:
                import logging
                logging.error(f"清理音频流失败: {e}")
            finally:
                self.stream = None
        
        # 清理数据，确保计数器为标量值
        self.frames.clear()  # 使用deque的clear方法
        self.read_count = 0
        self.valid_frame_count = 0
        self.silence_frame_count = 0
        self.debug_frame_count = 0
        
        # 强制垃圾回收
        import gc
        gc.collect()

    def cleanup(self):
        """公共清理方法，供外部调用"""
        try:
            self._cleanup()
        except Exception as e:
            import logging
            logging.error(f"清理音频捕获资源失败: {e}")
    
    def __del__(self):
        """析构函数，确保资源被正确释放"""
        try:
            self._cleanup()
        except Exception as e:
            import logging
            logging.error(f"析构时清理资源失败: {e}")

    def set_device(self, device_name=None):
        """设置音频输入设备"""
        try:
            # 停止当前录音
            self.stop_recording()
            
            # 重新初始化音频系统
            self._initialize_audio()
            
            if device_name is None or device_name == "系统默认":
                self.device_index = self._get_default_mic_index()
                return True
                
            # 查找指定设备
            devices = sd.query_devices()
            for i, device_info in enumerate(devices):
                if (device_info['max_input_channels'] > 0 and 
                    device_info['name'] == device_name):
                    self.device_index = i
                    return True
                    
            return False
            
        except Exception as e:
            import logging
            logging.error(f"设置音频设备失败: {e}")
            return False

    def _is_valid_audio(self, data):
        """检查音频数据是否有效（音量是否足够）"""
        audio_data = np.frombuffer(data, dtype=np.float32)
        # 直接使用RMS值判断，不使用移动平均
        volume = float(np.sqrt(np.mean(np.square(audio_data))))
        # 使用Python标量进行比较，避免NumPy数组比较错误
        is_valid = bool(volume > self.volume_threshold)
        
        # 更新调试计数器
        self.debug_frame_count += 1
        
        # 更新计数，确保计数器始终为标量值
        if is_valid:
            self.silence_frame_count = 0
            self.valid_frame_count = int(self.valid_frame_count) + 1
        else:
            self.silence_frame_count = int(self.silence_frame_count) + 1
            
        return is_valid

    def read_audio(self):
        """读取音频数据"""
        if self.stream and self.stream.active:
            # 在sounddevice中，数据通过回调函数自动收集
            # 检查是否有足够的数据
            if len(self.frames) > self.read_count:
                self.read_count = len(self.frames)
                # 返回最新的音频数据
                if self.frames:
                    latest_data = self.frames[-1]
                    # 修复: 移除静音检测导致的自动停止，让热键控制录音
                    # 在按键录音模式下，只有热键释放才应该停止录音
                    # if self.silence_frame_count >= self.max_silence_frames:
                    #     return None  # 返回None表示需要停止录音
                    return latest_data
        return b""

    def get_audio_data(self):
        """获取录音数据"""
        if not self.frames:
            return np.array([], dtype=np.float32)
            
        audio_data = b"".join(self.frames)
        data = np.frombuffer(audio_data, dtype=np.float32)
        
        # 检查是否有足够的有效音频
        # 确保使用标量值进行比较，避免NumPy数组比较错误
        try:
            # 强制转换为Python标量类型，避免NumPy数组比较
            if hasattr(self.valid_frame_count, 'item'):
                valid_count = self.valid_frame_count.item()
            elif hasattr(self.valid_frame_count, '__len__'):
                valid_count = int(self.valid_frame_count)
            else:
                valid_count = int(self.valid_frame_count)
                
            if hasattr(self.min_valid_frames, 'item'):
                min_valid = self.min_valid_frames.item()
            elif hasattr(self.min_valid_frames, '__len__'):
                min_valid = int(self.min_valid_frames)
            else:
                min_valid = int(self.min_valid_frames)
                
            # 使用Python标量进行比较
            if valid_count < min_valid:
                return np.array([], dtype=np.float32)
        except Exception as e:
            import logging
            logging.error(f"音频数据检查时出错: {e}")
            # 如果比较失败，返回原始数据
            pass
            
        return data

    def clear_recording_data(self):
        """清理录音数据"""
        self.frames.clear()
        self.read_count = 0
        self.valid_frame_count = 0
        self.silence_frame_count = 0
        self.debug_frame_count = 0
        
    def clear_buffer(self):
        """手动清理缓冲区"""
        self.frames.clear()

    def set_volume_threshold(self, threshold):
        """设置音量阈值（0-1000的值会被转换为0-0.02的浮点数）"""
        self.volume_threshold = (threshold / 1000.0) * 0.02

    def read_chunk(self):
        """读取音频块（兼容旧接口）"""
        if not self.stream or not self.stream.active:
            return None
            
        # 在sounddevice中，数据通过回调函数自动收集
        # 这里我们只需要检查是否有新数据
        if len(self.frames) > self.read_count:
            # 获取最新的帧
            latest_frame_data = self.frames[-1] if self.frames else None
            if latest_frame_data:
                self.read_count = len(self.frames)
                audio_data = np.frombuffer(latest_frame_data, dtype=np.float32)
                
                # 检查音频有效性并更新计数器
                if self._is_valid_audio(latest_frame_data):
                    self.valid_frame_count += 1
                    self.silence_frame_count = 0
                else:
                    self.silence_frame_count += 1
                    
                return audio_data
                
        return None