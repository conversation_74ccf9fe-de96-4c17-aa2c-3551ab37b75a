{"permissions": {"allow": ["Bash(ls:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./build_elegant_dmg.sh:*)", "Bash(./build_reliable_app.sh)", "Bash(./build_final_dmg.sh:*)", "Bash(./fix_pytorch_dylib.sh:*)", "Bash(conda activate:*)", "<PERSON><PERSON>(python:*)", "Bash(./rebuild_funasr_environment.sh:*)", "Bash(./build_funasr_fixed_app.sh:*)", "Bash(conda info:*)", "Bash(rm:*)", "Bash(./build_working_app.sh:*)", "Bash(./test_working_app.sh:*)", "<PERSON><PERSON>(cat:*)", "Bash(kill:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(open Dou-flow.app)", "Bash(cp:*)", "Bash(pgrep:*)", "Bash(./build_dou_flow_app.sh:*)", "Bash(/Users/<USER>/anaconda3/envs/wispr-flow-python311/bin/python test_paste_permission.py)", "<PERSON><PERSON>(source:*)", "Bash(git checkout:*)", "Bash(/Users/<USER>/anaconda3/envs/wispr-flow-python311/bin/python src/main.py)", "Bash(git add:*)", "Bash(./cleanup_project.sh:*)", "Bash(git restore:*)", "Bash(git reset:*)", "Bash(pip install:*)", "Bash(git ls-tree:*)", "<PERSON><PERSON>(pip uninstall:*)", "Bash(conda install:*)", "Bash(conda:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(git commit:*)", "<PERSON><PERSON>(sips:*)", "<PERSON><PERSON>(iconutil:*)", "<PERSON><PERSON>(mv:*)", "Bash(./build_voice_flow_app.sh:*)", "<PERSON><PERSON>(open:*)", "Bash(grep:*)", "Bash(dmesg:*)", "Bash(git stash:*)"], "deny": []}}