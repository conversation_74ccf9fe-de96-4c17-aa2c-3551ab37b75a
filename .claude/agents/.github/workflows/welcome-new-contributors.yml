---
name: Welcome New Contributors

'on':
  issues:
    types: [opened]
  pull_request_target:
    types: [opened]

jobs:
  welcome:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
      - name: Welcome first-time contributors
        uses: actions/first-interaction@v1
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          issue-message: |
            👋 **Welcome to the Claude Code Agents project,
            @${{ github.actor }}!**

            Thank you for opening your first issue! We appreciate your
            interest in contributing to our collection of specialized
            subagents.

            ## 📋 Community Guidelines

            Before we proceed, please take a moment to review our
            community standards:

            - 📖 **[Code of Conduct](.github/CODE_OF_CONDUCT.md)** -
              Our community standards and behavioral expectations
            - 🤝 **[Contributing Guidelines](.github/CONTRIBUTING.md)** -
              How to contribute effectively
            - 💬 **[GitHub Discussions]**
              (https://github.com/wshobson/agents/discussions)
              - For questions and general discussion

            ## 🛡️ Community Values

            We maintain a **respectful, inclusive, and professional
            environment**. Our community does not tolerate:
            - Hate speech, discrimination, or harassment
            - Personal attacks or inflammatory language
            - Spam, trolling, or off-topic content
            - Any content that violates our Code of Conduct

            ## ✅ What Makes a Great Issue

            **For Issues:**
            - Clear, specific problem descriptions
            - Steps to reproduce (for bugs)
            - Constructive feature requests with use cases
            - Professional, respectful language

            ## 🚀 Next Steps

            A maintainer will review your issue soon. Please feel free to:
            - Add any additional context if needed
            - Respond to questions from maintainers
            - Join our [Discussions] for questions
              (https://github.com/wshobson/agents/discussions)

            Thank you for helping make this project better! 🎉

          pr-message: |
            👋 **Welcome to the Claude Code Agents project,
            @${{ github.actor }}!**

            Thank you for opening your first pull request! We appreciate
            your interest in contributing to our collection of specialized
            subagents.

            ## 📋 Community Guidelines

            Before we proceed, please take a moment to review our
            community standards:

            - 📖 **[Code of Conduct](.github/CODE_OF_CONDUCT.md)** -
              Our community standards and behavioral expectations
            - 🤝 **[Contributing Guidelines](.github/CONTRIBUTING.md)** -
              How to contribute effectively
            - 💬 **[GitHub Discussions]**
              (https://github.com/wshobson/agents/discussions)
              - For questions and general discussion

            ## 🛡️ Community Values

            We maintain a **respectful, inclusive, and professional
            environment**. Our community does not tolerate:
            - Hate speech, discrimination, or harassment
            - Personal attacks or inflammatory language
            - Spam, trolling, or off-topic content
            - Any content that violates our Code of Conduct

            ## ✅ What Makes a Great Pull Request

            **For Pull Requests:**
            - Well-documented changes with clear descriptions
            - Following existing code style and conventions
            - Clear, descriptive commit messages
            - Addressing a specific issue or improvement
            - Including tests or examples where appropriate

            ## 🚀 Next Steps

            A maintainer will review your pull request soon.
            Please feel free to:
            - Add any additional context about your changes
            - Respond to feedback and questions from maintainers
            - Join our [Discussions] for questions
              (https://github.com/wshobson/agents/discussions)

            Thank you for helping make this project better! 🎉
