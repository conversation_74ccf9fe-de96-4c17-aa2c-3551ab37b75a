#!/usr/bin/env python3
"""
Dou-flow 应用最终验证脚本
验证应用是否能真正启动并正常工作
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def verify_app_structure():
    """验证应用包结构"""
    print("🔍 验证应用包结构...")
    
    app_path = Path("Dou-flow.app")
    if not app_path.exists():
        print("❌ Dou-flow.app 不存在")
        return False
    
    executable = app_path / "Contents/MacOS/Dou-flow"
    if not executable.exists():
        print("❌ 可执行文件不存在")
        return False
    
    if not os.access(executable, os.X_OK):
        print("❌ 可执行文件没有执行权限")
        return False
    
    resources = app_path / "Contents/Resources"
    if not resources.exists():
        print("❌ Resources目录不存在")
        return False
    
    main_py = resources / "main.py"
    if not main_py.exists():
        print("❌ main.py不存在")
        return False
    
    src_dir = resources / "src"
    if not src_dir.exists():
        print("❌ src目录不存在")
        return False
    
    print("✅ 应用包结构正确")
    return True

def verify_conda_environment():
    """验证conda环境"""
    print("🔍 验证conda环境...")
    
    try:
        result = subprocess.run(["conda", "env", "list"], capture_output=True, text=True, timeout=10)
        if "wispr-flow-python311" in result.stdout:
            print("✅ conda环境存在")
            return True
        else:
            print("❌ conda环境不存在")
            return False
    except Exception as e:
        print(f"❌ conda检查失败: {e}")
        return False

def test_direct_execution():
    """测试直接执行"""
    print("🔍 测试直接执行...")
    
    executable = Path("Dou-flow.app/Contents/MacOS/Dou-flow")
    
    try:
        # 启动应用
        process = subprocess.Popen(
            [str(executable)],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待5秒
        try:
            stdout, stderr = process.communicate(timeout=5)
            print(f"✅ 应用执行完成，返回码: {process.returncode}")
            if stderr:
                print(f"错误输出: {stderr}")
            return True
        except subprocess.TimeoutExpired:
            print("✅ 应用在后台运行")
            process.terminate()
            try:
                process.wait(timeout=3)
            except subprocess.TimeoutExpired:
                process.kill()
            return True
            
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False

def test_open_command():
    """测试open命令"""
    print("🔍 测试open命令...")
    
    try:
        result = subprocess.run(
            ["open", "Dou-flow.app"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("✅ open命令执行成功")
            time.sleep(3)  # 等待应用启动
            return True
        else:
            print(f"❌ open命令失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ open命令异常: {e}")
        return False

def check_logs():
    """检查日志文件"""
    print("🔍 检查日志文件...")
    
    log_files = {
        "startup": "~/dou-flow-startup.log",
        "error": "~/dou-flow-error.log",
        "debug": "~/dou-flow-debug.log"
    }
    
    for log_type, log_path in log_files.items():
        expanded_path = Path(log_path).expanduser()
        if expanded_path.exists():
            size = expanded_path.stat().st_size
            print(f"✅ {log_type}日志存在: {size} bytes")
            
            # 显示最后几行
            try:
                with open(expanded_path, 'r') as f:
                    lines = f.readlines()
                    if lines:
                        print(f"   最后几行:")
                        for line in lines[-2:]:
                            print(f"     {line.strip()}")
            except Exception as e:
                print(f"   读取失败: {e}")
        else:
            print(f"❌ {log_type}日志不存在")

def verify_python_environment():
    """验证Python环境"""
    print("🔍 验证Python环境...")
    
    # 检查启动日志中的Python信息
    startup_log = Path("~/dou-flow-startup.log").expanduser()
    if startup_log.exists():
        try:
            with open(startup_log, 'r') as f:
                content = f.read()
                
            if "Python路径: /Users/<USER>/miniconda3/envs/wispr-flow-python311/bin/python" in content:
                print("✅ Python环境路径正确")
            else:
                print("⚠️ Python环境路径可能不正确")
                
            if "Python版本: Python 3.11" in content:
                print("✅ Python版本正确")
            else:
                print("⚠️ Python版本可能不正确")
                
            return True
        except Exception as e:
            print(f"❌ 日志读取失败: {e}")
            return False
    else:
        print("❌ 启动日志不存在")
        return False

def main():
    """主函数"""
    print("🧪 Dou-flow 应用最终验证")
    print("=" * 50)
    
    tests = [
        ("应用包结构", verify_app_structure),
        ("Conda环境", verify_conda_environment),
        ("直接执行", test_direct_execution),
        ("Open命令", test_open_command),
        ("Python环境", verify_python_environment),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 检查日志
    print(f"\n📋 日志检查:")
    check_logs()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 验证结果总结")
    print("=" * 50)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 所有验证通过！")
        print("📱 应用可以正常启动和运行")
        print("🎯 用户体验：双击应用 → 显示模型下载对话框 → 选择下载或取消")
        return 0
    else:
        print(f"\n⚠️ {total - passed} 个验证失败")
        print("🔧 请检查失败的项目")
        return 1

if __name__ == "__main__":
    sys.exit(main())
